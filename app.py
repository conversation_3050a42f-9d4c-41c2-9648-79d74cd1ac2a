# %%
# import gunicorn
from saveCBBC_XLS import saveCBBC_XLS
import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import os
from sqlalchemy import create_engine
from dash import Dash, dcc,html, Input, Output, ctx, callback, dash_table
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import dash
# from dash_table import DataTable
from flask import send_from_directory, jsonify, request
import plotly.io as pio
from dotenv import load_dotenv
# from autoSaveCBBC import getCBBC
# from PIL import Image
import logging
import threading
import time
import requests
from bs4 import BeautifulSoup

# Import logging configuration
from logging_config import setup_logging

download_path = './download/'
out_path = os.environ.get('OUT_PATH', download_path)
logs_path = os.path.join(out_path, 'logs')
os.makedirs(logs_path, exist_ok=True)

# Setup logging with Hong Kong timezone and daily rotation
setup_logging(log_dir=logs_path)
# Get specialized loggers for different components
from logging_config import get_logger, get_hk_time, format_hk_time

# Setup component-specific loggers
main_logger = logging.getLogger('main_app')
market_logger = get_logger('market_status_worker')
auto_update_logger = get_logger('auto_update_worker')

platform = os.environ.get('platform')
if platform == 'sqlite':
    db = 'sqlite:///instance/app.db' # Use an instance directory for the DB file
    main_logger.info("Using SQLite database as platform is set to 'sqlite'.")
    # Ensure the instance directory exists
    if not os.path.exists('instance'):
        os.makedirs('instance')
        main_logger.info("Created 'instance' directory for SQLite database.")
elif platform == 'local':
    db = os.environ.get('LOCAL_DATABASE_URL')
elif platform == 'heroku':
    db = os.environ.get('HEROKU_DATABASE_URL')
else:
    db = os.environ.get('DATABASE_URL')

main_logger.info(f"Database platform: {platform}, URL configured: {bool(db)}")
out_path = os.environ.get('OUT_PATH', download_path)

if db is None:
    main_logger.error("No database URL found. Please check your environment variables.")
    raise ValueError("No database URL found. Please check your environment variables.")

remote_db = create_engine(db)
main_logger.info("Database connection established successfully")

# Global variables for market status and auto-update functionality
market_open_indicator = False
auto_update_enabled = True
update_interval = 15  # minutes
background_thread = None
market_check_thread = None
thread_lock = threading.Lock()

# Auto-update status tracking
last_update_time = None
next_update_time = None
last_update_rows = 0
last_update_success = True
last_market_check_time = None
next_market_check_time = None

main_logger.info("Application initialization completed")

def getPrice(t_name, t_time=''):
    #name = '^HSI'
    #txn_date='20210716'
    #d1=dt.datetime.today()- dt.timedelta(days=365)
    #d2=dt.date.today()
    #d1=dt.date(2021, 7, 12)
    #d1 = dt.datetime(int(t_time[0:4]), int(t_time[4:6]), int(t_time[6:8]), 13, 00, 0, 0, tzinfo=timezone.utc)
    main_logger.debug(f'Getting price for {t_name} on or before: {t_time}')
    ticker = yf.Ticker(t_name)
    # df = ticker.history(interval='1d',start=d1,end=d2, prepost=True)
    # df = ticker.history(interval='1d',end=t_time, prepost=True)
    df = ticker.history(interval='1d', prepost=True)
    if len(df) == 0: #Check valid
        main_logger.warning(f'Ticker {t_name} not found: Skipped')
        return None
    df.dropna(inplace=True)
    df['Date'] = pd.to_datetime(df.index)
    df = df.loc[:,['Date', 'Open', 'High', 'Low', 'Close']]
    main_logger.debug(f'Retrieved price data for {t_name}: {df.iloc[-1]["Close"]}')
    return df.iloc[-1]
    

def roundUpToMultiple(number, multiple):
    num = number + (multiple - 1)
    return num - (num % multiple)

def check_market_status():
    """
    Check Hong Kong Stock Exchange market status from market-clock.com
    Returns True if market is open, False if closed
    """
    global market_open_indicator
    try:
        url = "https://www.market-clock.com/markets/hkex/equities/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        market_logger.debug(f"Checking market status from: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # Look for various possible market status indicators
        status_indicators = [
            'div.market-status-text',
            'div.status-text',
            '.market-status',
            'div[class*="status"]',
            'span[class*="status"]',
            'div[class*="market"]'
        ]

        status_element = None
        for selector in status_indicators:
            status_element = soup.select_one(selector)
            if status_element:
                break

        if status_element:
            status_text = status_element.get_text().strip().lower()
            is_open = 'open' in status_text and 'closed' not in status_text
            market_logger.info(f"Market status from website: {status_text} -> {'OPEN' if is_open else 'CLOSED'}")
        else:
            # Fallback: analyze page content for time-based logic
            # Hong Kong market hours: 9:30 AM - 4:00 PM HKT (Monday-Friday)
            hk_time = get_hk_time()
            is_weekday = hk_time.weekday() < 5  # Monday=0, Friday=4
            current_hour = hk_time.hour
            current_minute = hk_time.minute

            # Market open: 9:30 AM - 4:00 PM HKT
            market_start = 9.5  # 9:30 AM
            market_end = 16.0   # 4:00 PM
            current_time = current_hour + current_minute / 60.0

            is_open = (is_weekday and
                      market_start <= current_time <= market_end)

            market_logger.warning(f"Market Still Closed at HK time {hk_time.strftime('%H:%M')}, "
                  f"Weekday: {is_weekday}, Market: {'OPEN' if is_open else 'CLOSED'}")

        with thread_lock:
            market_open_indicator = is_open

        market_logger.info(f"Market status check: {'OPEN' if is_open else 'CLOSED'} at {format_hk_time()}")
        return is_open

    except Exception as e:
        market_logger.error(f"Error checking market status: {e}")
        # On error, keep current status
        return market_open_indicator

def auto_update_worker():
    """
    Background worker that runs saveCBBC_XLS() at specified intervals
    when market is open and auto-update is enabled
    """
    global auto_update_enabled, update_interval, market_open_indicator
    global last_update_time, next_update_time, last_update_rows, last_update_success

    auto_update_logger.info("Auto-update worker started")

    while True:
        try:
            # Calculate next update time using Hong Kong timezone
            hk_now = get_hk_time()
            with thread_lock:
                next_update_time = hk_now + dt.timedelta(minutes=update_interval)

            if auto_update_enabled and market_open_indicator:
                auto_update_logger.info(f"Auto-update triggered at {format_hk_time()}")
                try:
                    # Record start time
                    start_time = get_hk_time()

                    # Run the update
                    saveCBBC_XLS()

                    # Count rows in the database after update
                    try:
                        df = pd.read_sql("SELECT COUNT(*) as row_count FROM hsi_cbbc_sum", remote_db)
                        row_count = df.iloc[0]['row_count']
                        auto_update_logger.debug(f"Database row count query successful: {row_count} rows")
                    except Exception as db_error:
                        row_count = 0
                        auto_update_logger.warning(f"Failed to count database rows: {db_error}")

                    # Update status variables
                    with thread_lock:
                        last_update_time = start_time
                        last_update_rows = row_count
                        last_update_success = True

                    auto_update_logger.info(f"Auto-update completed at {format_hk_time()}, {row_count} rows processed")

                except Exception as e:
                    with thread_lock:
                        last_update_time = get_hk_time()
                        last_update_success = False
                        last_update_rows = 0
                    auto_update_logger.error(f"Auto-update failed: {e}")
            else:
                auto_update_logger.debug(f"Auto-update skipped - enabled: {auto_update_enabled}, market open: {market_open_indicator}")

            # Sleep for the specified interval (in minutes)
            auto_update_logger.debug(f"Sleeping for {update_interval} minutes until next check")
            time.sleep(update_interval * 60)

        except Exception as e:
            auto_update_logger.error(f"Error in auto-update worker: {e}")
            time.sleep(60)  # Wait 1 minute before retrying

def get_next_check_interval():
    """
    Calculate the next check interval based on Hong Kong market hours
    Returns sleep time in seconds
    """
    hk_time = get_hk_time()
    current_hour = hk_time.hour
    current_minute = hk_time.minute
    is_weekday = hk_time.weekday() < 5  # Monday=0, Friday=4

    if not is_weekday:
        # Weekend: Sleep until next weekday 9:31AM
        # Calculate days until next Monday (weekday=0)
        days_ahead = (7 - hk_time.weekday()) % 7
        if days_ahead == 0:
            days_ahead = 1  # If today is Saturday/Sunday, go to next Monday
        next_weekday = hk_time + dt.timedelta(days=days_ahead)
        next_check = next_weekday.replace(hour=9, minute=31, second=0, microsecond=0)
        sleep_seconds = (next_check - hk_time).total_seconds()
        return max(sleep_seconds, 60)  # At least 1 minute

    current_time_decimal = current_hour + current_minute / 60.0

    if current_time_decimal < 8.0:
        # Early morning (before 8:00 AM): Check every 2 hours
        return 2 * 3600
    elif 8.0 <= current_time_decimal < 9.0:
        # Pre-market hour (8:00-9:00 AM): Check every 30 minutes
        return 30 * 60
    elif 9.0 <= current_time_decimal < 9.5:
        # Just before market open (9:00-9:30 AM): Check every 5 minutes
        return 5 * 60
    elif 9.5 <= current_time_decimal <= 16.0:
        # During market hours (9:30 AM - 4:00 PM): Check every 30 minutes
        return 30 * 60
    elif 16.0 < current_time_decimal <= 16.5:
        # Just after market close (4:00-4:30 PM): Check every 5 minutes
        return 5 * 60
    elif 16.5 < current_time_decimal <= 18.0:
        # Post-market hours (4:30-6:00 PM): Check every 30 minutes
        return 30 * 60
    else:
        # Evening/night (after 6:00 PM): Check every 2 hours
        return 2 * 3600

def market_status_worker():
    """
    Background worker that checks market status with intelligent scheduling
    based on Hong Kong Stock Exchange trading hours
    """
    global last_market_check_time, next_market_check_time

    market_logger.info("Market status worker started")

    while True:
        try:
            # Record check time using Hong Kong timezone
            check_time = get_hk_time()

            # Check market status
            check_market_status()

            # Calculate next check interval based on current time
            sleep_seconds = get_next_check_interval()

            # Update status tracking variables
            next_check_hk = check_time + dt.timedelta(seconds=sleep_seconds)

            with thread_lock:
                last_market_check_time = check_time
                next_market_check_time = next_check_hk

            # Log the scheduling decision
            market_logger.info(f"Market status checked at {check_time.strftime('%H:%M:%S HKT')}. {auto_update_enabled=}, "
                  f"Next check in {sleep_seconds//60} minutes at {next_check_hk.strftime('%H:%M:%S HKT')}")

            # Sleep until next check
            time.sleep(sleep_seconds)

        except Exception as e:
            market_logger.error(f"Error in market status worker: {e}")
            time.sleep(300)  # Wait 5 minutes before retrying

def start_background_tasks():
    """
    Start the background tasks for auto-update and market status checking
    """
    global background_thread, market_check_thread

    if background_thread is None or not background_thread.is_alive():
        background_thread = threading.Thread(target=auto_update_worker, daemon=True)
        background_thread.start()
        main_logger.info("Auto-update background task started")

    if market_check_thread is None or not market_check_thread.is_alive():
        market_check_thread = threading.Thread(target=market_status_worker, daemon=True)
        market_check_thread.start()
        main_logger.info("Market status background task started")

def plot_graph(idf, chart_type='overlay', df_cum=None, scale_ratio='auto'):
    global hi,cl,lo
    if len(idf)== 0:
        return dash.no_update, f'No rows found'
    
    txn_date = idf.txn_date[0][0:8]
    update_time=f"{idf.txn_date[0][-4:-2]}:{idf.txn_date[0][-2:]}"
    
    # Chart Body
    min_y, max_y =float(idf.CELL_RANGE.iloc[0]), float(idf.CELL_RANGE.iloc[-1])
    y_pos = np.arange(len(idf))
    y_label = sorted(list(idf.CELL_RANGE), reverse=True)
    bar_colors = idf.BULLBEAR.apply(lambda b: 'navy' if b =='Bull' else 'maroon' )
    
    if chart_type == 'overlay':
        # Original overlay chart
        max_width= max(max(idf.OS_VAL), max(idf.TURN_AMT))
        x_width= roundUpToMultiple(max_width, float(f"1E+{len(str(int(max_width)))-2}"))
        
        fig = go.Figure(data=[
            go.Bar(name='剩餘價值$', x=idf.OS_VAL, y=idf.CELL_RANGE, orientation='h', 
                marker_color =bar_colors),
            go.Bar(name='當日成交$', x=idf.TURN_AMT, y=idf.CELL_RANGE,orientation='h',
                opacity=0.5, marker_color='cyan')
            ])
        fig.update_layout(barmode='overlay')
        title_text = f'恒指牛熊證 街貨回收價分佈及當天成交 {txn_date[0:4]}-{txn_date[4:6]}-{txn_date[-2:]} @{update_time}'
        
    elif chart_type == 'sidebyside':
        # Side by side charts with user-configurable x-axis scaling
        from plotly.subplots import make_subplots

        max_width_os = max(idf.OS_VAL)
        max_width_turn = max(idf.TURN_AMT)

        # Calculate automatic scale ratio if 'auto' is selected
        if scale_ratio == 'auto':
            data_ratio = max_width_turn / max_width_os if max_width_os > 0 else 1
            main_logger.debug(f"Data ratio (TURN_AMT/OS_VAL): {data_ratio:.2f}")

            # Determine appropriate scale ratio based on data
            # Choose ratio that allows TURN_AMT data to use 50-80% of x-axis range
            if data_ratio >= 150:
                applied_ratio = 200
            elif data_ratio >= 50:  # Changed from 75 to 50
                applied_ratio = 100
            elif data_ratio >= 25:  # Changed from 35 to 25
                applied_ratio = 50
            elif data_ratio >= 7:
                applied_ratio = 10
            else:
                applied_ratio = 1
            main_logger.info(f"Auto-selected scale ratio: {applied_ratio}:1")
        else:
            applied_ratio = scale_ratio
            main_logger.info(f"User-selected scale ratio: {applied_ratio}:1")

        # Calculate x-axis ranges based on the applied ratio
        # OS_VAL chart uses its natural scale
        x_max_os = max_width_os

        # TURN_AMT chart scale is adjusted by the ratio
        # If ratio is 100, TURN_AMT axis will be 100x wider than OS_VAL axis
        x_max_turn = max_width_os * applied_ratio

        main_logger.debug(f"X-axis ranges - OS_VAL: 0 to {x_max_os:,.0f}, TURN_AMT: 0 to {x_max_turn:,.0f}")

        fig = make_subplots(
            rows=1, cols=2,
            shared_yaxes=True,
            subplot_titles=('剩餘價值$', '當日成交$'),
            horizontal_spacing=0.05,
            specs=[[{"secondary_y": False}, {"secondary_y": False}]]
        )

        # Add OS_VAL chart
        fig.add_trace(
            go.Bar(name='剩餘價值$', x=idf.OS_VAL, y=idf.CELL_RANGE, orientation='h',
                   marker_color=bar_colors, showlegend=True),
            row=1, col=1
        )

        # Add TURN_AMT chart
        fig.add_trace(
            go.Bar(name='當日成交$', x=idf.TURN_AMT, y=idf.CELL_RANGE, orientation='h',
                   marker_color='cyan', showlegend=True),
            row=1, col=2
        )

        # Configure x-axis ranges with the specified ratio
        fig.update_xaxes(range=[0, x_max_os], row=1, col=1)
        fig.update_xaxes(range=[0, x_max_turn], row=1, col=2)

        # Configure y-axes - show on both sides for better readability
        fig.update_yaxes(
            tickmode='linear',
            tickformat='00000',
            dtick=200,
            side='left',
            row=1, col=1
        )
        fig.update_yaxes(
            tickmode='linear',
            tickformat='00000',
            dtick=200,
            side='right',
            showticklabels=True,
            row=1, col=2
        )

        max_width = max(x_max_os, x_max_turn)
        title_text = f'恒指牛熊證 街貨回收價分佈及當天成交 (並排) {txn_date[0:4]}-{txn_date[4:6]}-{txn_date[-2:]} @{update_time}'

        # Add scale ratio information to title
        if applied_ratio != 1:
            title_text += f' [比例: {applied_ratio}:1]'

        # Store the calculated ranges for use in horizontal lines
        adjusted_max_os = x_max_os
        adjusted_max_turn = x_max_turn
        
    elif chart_type == 'turnamt':
        # TURN_AMT only
        max_width = max(idf.TURN_AMT)
        x_width= roundUpToMultiple(max_width, float(f"1E+{len(str(int(max_width)))-2}"))
        
        fig = go.Figure(data=[
            go.Bar(name='當日成交$', x=idf.TURN_AMT, y=idf.CELL_RANGE, orientation='h',
                   marker_color='cyan')
        ])
        title_text = f'恒指牛熊證 當天成交 {txn_date[0:4]}-{txn_date[4:6]}-{txn_date[-2:]} @{update_time}'
        
    elif chart_type == 'cumosval':
        # OS_VAL only - need df_cum data
        if df_cum is None or len(df_cum) == 0:
            return dash.no_update, f'No OS_VAL data found'
            
        max_width = max(df_cum.CUM_OS_VAL)
        x_width= roundUpToMultiple(max_width, float(f"1E+{len(str(int(max_width)))-2}"))
        
        cum_bar_colors = df_cum.BULLBEAR.apply(lambda b: 'navy' if b =='Bull' else 'maroon' )
        
        if chart_type == 'cumosval':
            fig = go.Figure(data=[
                go.Scatter(name='累計剩餘價值$', x=df_cum.CUM_OS_VAL, y=df_cum.CallLv, orientation='h',
                           mode='lines', marker=dict(color=cum_bar_colors), fill='tozerox')
            ])
        else:
            fig = go.Figure(data=[
                go.Bar(name='累計剩餘價值$', x=df_cum.CUM_OS_VAL, y=df_cum.CallLv, orientation='h',
                       marker_color=cum_bar_colors)
            ])
        title_text = f'恒指牛熊證 累計剩餘價值 {txn_date[0:4]}-{txn_date[4:6]}-{txn_date[-2:]} @{update_time}'
    
    # Common layout updates
    fig.update_layout(
        yaxis = dict(
            tickmode = 'linear',
            tickformat = '00000',
            dtick = 200
        )
    )
    fig.update_layout(title_text=title_text,
        title_font_size=20, title_x=0.5, title_y=0.93, title_font_color='navy',
        height=800
        #, plot_bgcolor='cornsilk'
        )        
    fig.update_layout(legend=dict(
        yanchor="bottom",
        y=0.01,
        xanchor="right",
        x=0.99))
    
    # horizontal line indicating the H/L
    #hi,cl,lo=25500,25000,23500
    try:
        p= getPrice('^HSI', txn_date)
        if p is not None:
            hi,cl,lo=round(p.High),round(p.Close),round(p.Low)
        else:
            hi,cl,lo=0,0,0
        
        if chart_type == 'sidebyside':
            # Add lines to both subplots with appropriate scaling
            # Left subplot (OS_VAL) - use adjusted_max_os
            fig.add_shape(type="line",x0=0, y0=hi, x1=adjusted_max_os, y1=hi,
                line_dash="dot", line=dict(color="red",width=1), row=1, col=1)
            fig.add_shape(type="line",x0=0, y0=cl, x1=adjusted_max_os, y1=cl,
                line=dict(color="Red",width=1), row=1, col=1)
            fig.add_shape(type="line",x0=0, y0=lo, x1=adjusted_max_os, y1=lo,
                line_dash="dot", line=dict(color="red",width=1), row=1, col=1)

            # Right subplot (TURN_AMT) - use adjusted_max_turn
            fig.add_shape(type="line",x0=0, y0=hi, x1=adjusted_max_turn, y1=hi,
                line_dash="dot", line=dict(color="red",width=1), row=1, col=2)
            fig.add_shape(type="line",x0=0, y0=cl, x1=adjusted_max_turn, y1=cl,
                line=dict(color="Red",width=1), row=1, col=2)
            fig.add_shape(type="line",x0=0, y0=lo, x1=adjusted_max_turn, y1=lo,
                line_dash="dot", line=dict(color="red",width=1), row=1, col=2)

            # Add annotation to the right subplot
            fig.add_annotation(x=adjusted_max_turn*0.8, y=hi,text=f"{hi=}, {lo=}, {cl=}",
                yshift=10, showarrow=False,font=dict( size=16, color="red"), row=1, col=2)
        else:
            fig.add_shape(type="line",x0=0, y0=hi, x1=max_width, y1=hi,
                line_dash="dot", line=dict(color="red",width=1))
            fig.add_shape(type="line",x0=0, y0=cl, x1=max_width, y1=cl,
                line=dict(color="Red",width=1))
            fig.add_shape(type="line",x0=0, y0=lo, x1=max_width, y1=lo,
                line_dash="dot", line=dict(color="red",width=1))
            fig.add_annotation(x=max_width*0.8, y=hi,text=f"{hi=}, {lo=}, {cl=}", 
                yshift=10, showarrow=False,font=dict( size=16, color="red"))
    except Exception as e:
        main_logger.warning(f'Failed to get price data: {e}')

    try:
        # fig.update_layout(font_family='SimHei')
        # Remove old figure if exit
        if os.path.exists(download_path+"cbbc.png"):
            os.remove(download_path+"cbbc.png")
        fig.write_image(download_path+"cbbc.png", engine='kaleido')
        main_logger.debug('Chart image saved successfully (cbbc.png)')
    except Exception as e:
        main_logger.error(f'Failed to save chart image with Kaleido: {e}')
    try:
        if os.path.exists(download_path+"cbbc_large.png"):
            os.remove(download_path+"cbbc_large.png")
        pio.write_image(fig, download_path+"cbbc_large.png", width=800, height=1200)
        main_logger.debug('Large chart image saved successfully (cbbc_large.png)')
    except Exception as e:
        main_logger.error(f'Failed to save large chart image with pio: {e}')
    return fig

#%%
tname = 'hsi_cbbc_sum'
# Get Summary Table
q=f"select * from {tname};"
q_sum='select * from hsi_cbbc_sum , (select max("CELL_RANGE") as max_bull from hsi_cbbc_sum where "BULLBEAR"=\'Bull\') bulls where "CELL_RANGE" between max_bull -2000 and max_bull + 2000;'
# q_cum='select * from hsi_cbbc_cum , (select max("CELL_RANGE") as max_bull from hsi_cbbc_sum where "BULLBEAR"=\'Bull\') bulls where "CallLv" between max_bull -1000 and max_bull + 1000 order by "CallLv" desc;'
q_cum='select * from hsi_cbbc_cum where "CUM_OS_VAL" between 0 and 30000000 order by "CallLv" desc;'
main_logger.debug(f"Database query: {q}")
# _app = flask.Flask(__name__)
# app = dash.Dash(__name__, server=_app)
app = dash.Dash(__name__, )
server = app.server
hi,cl,lo=0,0,0
app.layout = html.Div([
    html.Div([
        html.H4("CBBC Chart"),
        html.Button('Refresh', id='btn-nclicks-1', n_clicks=0),
        html.Div(id='my_msg', children=[html.Div(id='info_msg')]),
    ], style={'display': 'flex', 'alignItems': 'center'}),

    # Market Status and Auto-Update Controls (moved above Chart Type)
    html.Div([
        # Left side: Controls
        html.Div([
            html.Div([
                html.Label("Market Status:", style={'marginRight': '10px', 'fontWeight': 'bold', 'fontSize': '14px'}),
                dcc.RadioItems(
                    id='market-status-radio',
                    options=[
                        {'label': 'Open', 'value': True},
                        {'label': 'Closed', 'value': False}
                    ],
                    value=market_open_indicator,
                    inline=True,
                    style={'marginLeft': '10px'}
                )
            ], style={'marginBottom': '8px'}),

            html.Div([
                html.Label("Auto-Update:", style={'marginRight': '10px', 'fontWeight': 'bold', 'fontSize': '14px'}),
                dcc.RadioItems(
                    id='auto-update-radio',
                    options=[
                        {'label': 'Enabled', 'value': True},
                        {'label': 'Disabled', 'value': False}
                    ],
                    value=auto_update_enabled,
                    inline=True,
                    style={'marginLeft': '10px', 'marginRight': '15px'}
                ),
                html.Label("Interval:", style={'marginRight': '8px', 'fontWeight': 'bold', 'fontSize': '14px'}),
                dcc.Dropdown(
                    id='update-interval-dropdown',
                    options=[
                        {'label': '5 min', 'value': 5},
                        {'label': '15 min', 'value': 15},
                        {'label': '30 min', 'value': 30},
                        {'label': '60 min', 'value': 60}
                    ],
                    value=update_interval,
                    style={'width': '80px', 'display': 'inline-block', 'fontSize': '12px'}
                )
            ], style={'display': 'flex', 'alignItems': 'center'})
        ], style={'flex': '1', 'paddingRight': '20px'}),

        # Right side: Status Information
        html.Div([
            html.Div(id='status-info', children=[
                html.Div("Status: Initializing...", style={'fontSize': '12px', 'marginBottom': '4px'}),
                html.Div("Last Update: --", style={'fontSize': '12px', 'marginBottom': '4px'}),
                html.Div("Next Update: --", style={'fontSize': '12px', 'marginBottom': '4px'}),
                html.Div("Data Rows: --", style={'fontSize': '12px'})
            ])
        ], style={'flex': '1', 'paddingLeft': '20px', 'borderLeft': '1px solid #ddd'})
    ], style={'margin': '10px 0', 'padding': '10px', 'backgroundColor': '#f8f8f0', 'borderRadius': '5px', 'display': 'flex'}),

    # Chart Type Controls
    html.Div([
        html.Label("Chart Type:", style={'marginRight': '10px', 'fontWeight': 'bold'}),
        dcc.RadioItems(
            id='chart-type-radio',
            options=[
                {'label': '單圖比對', 'value': 'overlay'},
                {'label': '左右比對', 'value': 'sidebyside'},
                {'label': '當日成交金額', 'value': 'turnamt'},
                {'label': '累計剩餘價值', 'value': 'cumosval'}
            ],
            value='sidebyside',  # Changed default to sidebyside
            inline=True,
            style={'marginLeft': '10px'}
        )
    ], style={'margin': '10px 0', 'padding': '10px', 'backgroundColor': '#f0f0f0', 'borderRadius': '5px'}),

    # Scale ratio control for side-by-side charts
    html.Div([
        html.Label("Scale Ratio (TURN_AMT : OS_VAL):", style={'marginRight': '10px', 'fontWeight': 'bold'}),
        dcc.RadioItems(
            id='scale-ratio-radio',
            options=[
                {'label': '1:1 (Same Scale)', 'value': 1},
                {'label': '10:1', 'value': 10},
                {'label': '50:1', 'value': 50},
                {'label': '100:1', 'value': 100},
                {'label': '200:1', 'value': 200},
                {'label': 'Auto', 'value': 'auto'}
            ],
            value='auto',
            inline=True,
            style={'marginLeft': '10px'}
        )
    ], id='scale-ratio-div', style={'margin': '10px 0', 'padding': '10px', 'backgroundColor': '#e8f4f8', 'borderRadius': '5px', 'display': 'block'}),
    dcc.Graph(id='my_graph'),
    html.Div(id='tbl_div', children = [dash_table.DataTable(id='tbl')]),
    # Interval component for status updates
    dcc.Interval(
        id='status-update-interval',
        interval=5000,  # Update every 5 seconds
        n_intervals=0
    ),
])

# Callback to show/hide scale ratio control based on chart type
@app.callback(
    Output('scale-ratio-div', 'style'),
    Input('chart-type-radio', 'value')
)
def toggle_scale_ratio_control(chart_type):
    if chart_type == 'sidebyside':
        return {'margin': '10px 0', 'padding': '10px', 'backgroundColor': '#e8f4f8', 'borderRadius': '5px', 'display': 'block'}
    else:
        return {'margin': '10px 0', 'padding': '10px', 'backgroundColor': '#e8f4f8', 'borderRadius': '5px', 'display': 'none'}

# Callback to update status information
@app.callback(
    Output('status-info', 'children'),
    Input('status-update-interval', 'n_intervals')
)
def update_status_info(n_intervals):
    """Update the status information display"""
    global last_update_time, next_update_time, last_update_rows, last_update_success
    global last_market_check_time, next_market_check_time, market_open_indicator, auto_update_enabled

    try:
        # Current time in Hong Kong timezone
        now = get_hk_time()

        # Market status
        market_status = "🟢 OPEN" if market_open_indicator else "🔴 CLOSED"
        auto_status = "✅ Enabled" if auto_update_enabled else "❌ Disabled"

        # Last update info
        if last_update_time:
            # Convert to Hong Kong timezone if needed
            if last_update_time.tzinfo is None:
                last_update_hk = last_update_time.replace(tzinfo=dt.timezone.utc).astimezone(dt.timezone(dt.timedelta(hours=8)))
            else:
                last_update_hk = last_update_time.astimezone(dt.timezone(dt.timedelta(hours=8)))

            last_update_str = last_update_hk.strftime("%H:%M:%S HKT")
            if last_update_success:
                last_update_str += f" ✅ ({last_update_rows:,} rows)"
            else:
                last_update_str += " ❌ Failed"
        else:
            last_update_str = "Not yet executed"

        # Next update info
        if next_update_time and auto_update_enabled and market_open_indicator:
            # Convert to Hong Kong timezone if needed
            if next_update_time.tzinfo is None:
                next_update_hk = next_update_time.replace(tzinfo=dt.timezone.utc).astimezone(dt.timezone(dt.timedelta(hours=8)))
            else:
                next_update_hk = next_update_time.astimezone(dt.timezone(dt.timedelta(hours=8)))

            time_until_next = next_update_hk - now
            if time_until_next.total_seconds() > 0:
                minutes_left = int(time_until_next.total_seconds() / 60)
                next_update_str = f"{next_update_hk.strftime('%H:%M:%S HKT')} (in {minutes_left}m)"
            else:
                next_update_str = "Due now"
        else:
            next_update_str = "Paused" if not auto_update_enabled else "Market closed"

        # Market check info
        if last_market_check_time:
            # Convert to Hong Kong timezone if needed
            if last_market_check_time.tzinfo is None:
                last_check_hk = last_market_check_time.replace(tzinfo=dt.timezone.utc).astimezone(dt.timezone(dt.timedelta(hours=8)))
            else:
                last_check_hk = last_market_check_time.astimezone(dt.timezone(dt.timedelta(hours=8)))
            last_check_str = last_check_hk.strftime("%H:%M:%S HKT")
        else:
            last_check_str = "Not yet checked"

        if next_market_check_time:
            next_check_str = next_market_check_time.strftime("%H:%M:%S HKT")
        else:
            next_check_str = "Calculating..."

        return [
            html.Div(f"Market: {market_status} | Auto-Update: {auto_status}",
                    style={'fontSize': '12px', 'marginBottom': '4px', 'fontWeight': 'bold'}),
            html.Div(f"Last Update: {last_update_str}",
                    style={'fontSize': '11px', 'marginBottom': '3px'}),
            html.Div(f"Next Update: {next_update_str}",
                    style={'fontSize': '11px', 'marginBottom': '3px'}),
            html.Div(f"Market Check: {last_check_str} → {next_check_str}",
                    style={'fontSize': '11px', 'color': '#666'})
        ]

    except Exception as e:
        return [html.Div(f"Status Error: {str(e)}", style={'fontSize': '11px', 'color': 'red'})]

# Callbacks for market status and auto-update controls
@app.callback(
    Output('market-status-radio', 'value'),
    Input('market-status-radio', 'value')
)
def update_market_status(value):
    global market_open_indicator
    if value is not None:
        with thread_lock:
            market_open_indicator = value
        main_logger.info(f"Market status manually set to: {'OPEN' if value else 'CLOSED'}")
    return market_open_indicator

@app.callback(
    Output('auto-update-radio', 'value'),
    Input('auto-update-radio', 'value')
)
def update_auto_update_status(value):
    global auto_update_enabled
    if value is not None:
        auto_update_enabled = value
        main_logger.info(f"Auto-update {'enabled' if value else 'disabled'}")
    return auto_update_enabled

@app.callback(
    Output('update-interval-dropdown', 'value'),
    Input('update-interval-dropdown', 'value')
)
def update_interval_setting(value):
    global update_interval
    if value is not None and value in [5, 15, 30, 60]:
        update_interval = value
        main_logger.info(f"Update interval set to {value} minutes")
    return update_interval

@app.callback(
    [Output('my_graph', 'figure'),
    Output('tbl_div', 'children'),
    Output('info_msg', 'children')],
    [Input('btn-nclicks-1', 'n_clicks'),
     Input('chart-type-radio', 'value'),
     Input('scale-ratio-radio', 'value')]
    # [Input('update_trigger', 'n_intervals')]
)
def update_tbl(i, chart_type, scale_ratio):
    global last_update_time, last_update_rows, last_update_success

    if "btn-nclicks-1" == ctx.triggered_id:
        # Log timestamp using Hong Kong timezone
        hk_time = get_hk_time()
        msg = f"REFRESH started: {format_hk_time(hk_time)}"
        main_logger.info(msg)
        try:
            start_time = hk_time
            saveCBBC_XLS()

            # Count rows after manual update
            try:
                df = pd.read_sql("SELECT COUNT(*) as row_count FROM hsi_cbbc_sum", remote_db)
                row_count = df.iloc[0]['row_count']
            except Exception as db_error:
                row_count = 0
                main_logger.warning(f"Failed to count rows after manual update: {db_error}")

            # Update status variables
            with thread_lock:
                last_update_time = start_time
                last_update_rows = row_count
                last_update_success = True

            msg = f"REFRESH completed: {format_hk_time()}, {row_count} rows processed"
            main_logger.info(msg)
        except Exception as e:
            with thread_lock:
                last_update_time = get_hk_time()
                last_update_success = False
                last_update_rows = 0
            main_logger.error(f'Failed to saveCBBC_XLS: {e}')
            msg = f"REFRESH failed: {format_hk_time()}"
    else:
        msg = "---"
    main_logger.debug(f"update_tbl called - clicks: {i}, chart_type: {chart_type}, scale_ratio: {scale_ratio}")

    # Re-read data from database after saveCBBC_XLS() to get fresh data
    df= pd.read_sql(q_sum, remote_db)
    main_logger.debug(f"Data retrieved: {df.txn_date[0][0:8]}, {len(df)} rows from query: {q_sum[:50]}...")

    df_cum = pd.read_sql(q_cum, remote_db)

    # Generate fresh chart with updated data based on chart type and scale ratio
    cbbc_chart= plot_graph(df, chart_type, df_cum, scale_ratio)
    # df_cum['CallLv']= df_cum['CallLv'].map('{:,.0f}'.format)
    # df_cum['CallLv'] = df_cum['CallLv'].str.replace(',', '')  # Remove commas if present
    # df_cum['CallLv'] = pd.to_numeric(df_cum['CallLv'])  # Convert to numeric
    df_list = df_cum[['CallLv', 'BULLBEAR', 'OS_VAL', 'TURN_AMT', 'CUM_OS_VAL']]
    df_list.columns = ['回收價', 'Bull/Bear', '剩餘價值$', '當日成交金額$', '累計剩餘價值$']
    df_cum.OS_VAL = df_cum.OS_VAL.map('${:,.0f}'.format)
    df_cum.TURN_AMT = df_cum.TURN_AMT.map('${:,.0f}'.format)
    df_cum.CUM_OS_VAL = df_cum.CUM_OS_VAL.map('${:,.0f}'.format)        
    df_list= df_cum[['CallLv','BULLBEAR','OS_VAL','TURN_AMT', 'CUM_OS_VAL']]
    df_list.columns=['回收價', 'Bull/Bear','剩餘價值$', '當日成交金額$', '累計剩餘價值$']
    # Add a '已收回' column based on the condition as Y or N
    df_list['已收回'] = np.where((df_list['回收價'] >= lo) & (df_list['回收價'] <= hi), 'Y', 'N')
    # Prepare to List the SUMMARY Table
    # df_list= df[['BULLBEAR','CELL_RANGE','OS_VAL','TURN_AMT']]
    # df_list.OS_VAL = df_list.OS_VAL.map('${:,.0f}'.format)
    # df_list.TURN_AMT = df_list.TURN_AMT.map('${:,.0f}'.format)
    # df_list.columns=['Bull/Bear','回收價', '剩餘價值$', '當日成交金額$']
    tbl = dash_table.DataTable(
        id='payoff-table', data=df_list.to_dict('records'),
        columns=[{"name": i, "id": i} for i in df_list.columns],  
        style_cell_conditional=[
            {
                'if': {'column_id': c},
                'textAlign': 'left'
            } for c in ['回收價', 'Bull/Bear']
        ],
        style_data_conditional=[
            # Grey out rows where 回收價 is between 23000 and 23600
            # {
            #     'if': {'filter_query': '{回收價} >= {lo} and {回收價} <= {hi}'},
            #     'backgroundColor': 'lightgrey',
            #     'color': 'grey'
            # },                 
            # Grey out rows where 'highlight' is True
            {
                'if': {'filter_query': '{已收回} = "Y"'},
                'backgroundColor': 'lightgrey'
                # ,'color': 'red'
            },               
            {
                'if': {'row_index': 'odd'},
                'backgroundColor': 'rgb(248, 248, 248)'
            },
            {
              'if': {'filter_query': '{Bull/Bear} = "Bull"', 'column_id': 'Bull/Bear'},
              'color': 'navy'
            },
            {
              'if': {'filter_query': '{Bull/Bear} = "Bear"', 'column_id': 'Bull/Bear'},
              'color': 'red'
            },
        ],
        style_header={
            'backgroundColor': 'rgb(230, 230, 230)',
            'fontWeight': 'bold'
        }        
    )

    return (cbbc_chart, tbl, msg)

# API Endpoints
@server.route('/api/market-status', methods=['GET'])
def api_market_status():
    """Get current market status"""
    hk_time = get_hk_time()
    main_logger.debug("API: Market status requested")
    return jsonify({
        'market_open': market_open_indicator,
        'auto_update_enabled': auto_update_enabled,
        'update_interval': update_interval,
        'last_checked': hk_time.isoformat(),
        'timezone': 'Hong Kong (UTC+8)'
    })

@server.route('/api/toggle-auto-update', methods=['POST'])
def api_toggle_auto_update():
    """Toggle auto-update functionality"""
    global auto_update_enabled

    data = request.get_json() if request.is_json else {}
    if 'enabled' in data:
        auto_update_enabled = bool(data['enabled'])
    else:
        auto_update_enabled = not auto_update_enabled

    return jsonify({
        'auto_update_enabled': auto_update_enabled,
        'message': f"Auto-update {'enabled' if auto_update_enabled else 'disabled'}"
    })

@server.route('/api/set-update-interval', methods=['POST'])
def api_set_update_interval():
    """Set the auto-update interval"""
    global update_interval

    data = request.get_json() if request.is_json else {}
    if 'interval' in data:
        new_interval = int(data['interval'])
        if new_interval in [5, 15, 30, 60]:
            update_interval = new_interval
            return jsonify({
                'update_interval': update_interval,
                'message': f"Update interval set to {update_interval} minutes"
            })
        else:
            return jsonify({'error': 'Invalid interval. Must be 5, 15, 30, or 60 minutes'}), 400
    else:
        return jsonify({'error': 'Missing interval parameter'}), 400

@server.route('/api/manual-update', methods=['POST'])
def api_manual_update():
    """Manually trigger CBBC data update"""
    try:
        start_time = get_hk_time()
        main_logger.info(f"API: Manual update triggered at {format_hk_time(start_time)}")
        saveCBBC_XLS()
        end_time = get_hk_time()
        duration = (end_time - start_time).total_seconds()

        main_logger.info(f"API: Manual update completed in {duration:.2f} seconds")
        return jsonify({
            'success': True,
            'message': 'Manual update completed successfully',
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'timezone': 'Hong Kong (UTC+8)'
        })
    except Exception as e:
        main_logger.error(f"API: Manual update failed: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Manual update failed'
        }), 500

@server.route('/hi')
def hello_world():
    return 'Hello, Docker!'

#  retrieve file from 'static/images' directory
@server.route('/img/<arg>')
def send_image(arg):
    main_logger.debug(f"Image requested: {arg}")
    filename= arg.split(':')[0]
    return send_from_directory(download_path, filename)

# @app.route('/update_cbbc')
# def update_cbbc():
#     print("update_cbbc")
#     try:
#         t="0000"
#         t = getCBBC()
#         r=f"getCBBC() completed at {t=}"
#     except:
#         r= "getCBBC() Failed"
#     print(r)
#     return r

# @app.route('/run_cbbc')
# def run_cbbc():
#     print("run autoSaveCBBC")
#     try:
#         t="0000"
#         t = runCBBC()
#         r=f"run() completed at {t=}"
#     except:
#         r= "run() Failed"
#     print(r)
#     return r

# Start background tasks when the app starts
start_background_tasks()

if __name__ == '__main__':
    app.run_server(debug=True)

# %%
# t = getCBBC()
# %%
