#!/usr/bin/env python3
"""
Test script for the enhanced market status worker
Tests the intelligent scheduling logic for different times of day
"""

import datetime as dt
from unittest.mock import patch

def get_next_check_interval():
    """
    Calculate the next check interval based on Hong Kong market hours
    Returns sleep time in seconds
    """
    hk_time = dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))
    current_hour = hk_time.hour
    current_minute = hk_time.minute
    is_weekday = hk_time.weekday() < 5  # Monday=0, Friday=4
    
    if not is_weekday:
        # Weekend: Check once every 4 hours
        return 4 * 3600
    
    current_time_decimal = current_hour + current_minute / 60.0
    
    if current_time_decimal < 8.0:
        # Early morning (before 8:00 AM): Check every 2 hours
        return 2 * 3600
    elif 8.0 <= current_time_decimal < 9.0:
        # Pre-market hour (8:00-9:00 AM): Check every 30 minutes
        return 30 * 60
    elif 9.0 <= current_time_decimal < 9.5:
        # Just before market open (9:00-9:30 AM): Check every 5 minutes
        return 5 * 60
    elif 9.5 <= current_time_decimal <= 16.0:
        # During market hours (9:30 AM - 4:00 PM): Check every 30 minutes
        return 30 * 60
    elif 16.0 < current_time_decimal <= 16.5:
        # Just after market close (4:00-4:30 PM): Check every 5 minutes
        return 5 * 60
    elif 16.5 < current_time_decimal <= 18.0:
        # Post-market hours (4:30-6:00 PM): Check every 30 minutes
        return 30 * 60
    else:
        # Evening/night (after 6:00 PM): Check every 2 hours
        return 2 * 3600

def test_scheduling_logic():
    """Test the scheduling logic for different times and days"""
    
    # Test cases: (hour, minute, weekday, expected_interval_minutes, description)
    test_cases = [
        # Weekend tests
        (10, 0, 5, 240, "Saturday 10:00 AM - Weekend"),
        (14, 30, 6, 240, "Sunday 2:30 PM - Weekend"),
        
        # Weekday tests
        (6, 0, 1, 120, "Tuesday 6:00 AM - Early morning"),
        (7, 45, 2, 120, "Wednesday 7:45 AM - Early morning"),
        (8, 15, 3, 30, "Thursday 8:15 AM - Pre-market"),
        (8, 45, 4, 30, "Friday 8:45 AM - Pre-market"),
        (9, 10, 0, 5, "Monday 9:10 AM - Just before open"),
        (9, 25, 1, 5, "Tuesday 9:25 AM - Just before open"),
        (9, 30, 2, 30, "Wednesday 9:30 AM - Market open"),
        (11, 0, 3, 30, "Thursday 11:00 AM - During market"),
        (14, 30, 4, 30, "Friday 2:30 PM - During market"),
        (16, 0, 0, 30, "Monday 4:00 PM - Market close"),
        (16, 10, 1, 5, "Tuesday 4:10 PM - Just after close"),
        (16, 25, 2, 5, "Wednesday 4:25 PM - Just after close"),
        (16, 45, 3, 30, "Thursday 4:45 PM - Post-market"),
        (17, 30, 4, 30, "Friday 5:30 PM - Post-market"),
        (18, 30, 0, 120, "Monday 6:30 PM - Evening"),
        (22, 0, 1, 120, "Tuesday 10:00 PM - Night"),
        (2, 0, 2, 120, "Wednesday 2:00 AM - Night"),
    ]
    
    print("Testing Enhanced Market Status Worker Scheduling Logic")
    print("=" * 60)
    
    for hour, minute, weekday, expected_minutes, description in test_cases:
        # Create a mock datetime for Hong Kong timezone
        mock_time = dt.datetime(2024, 1, 1, hour, minute, 0, tzinfo=dt.timezone(dt.timedelta(hours=8)))
        # Adjust the date to match the weekday (Monday=0, Sunday=6)
        days_to_add = weekday - mock_time.weekday()
        mock_time = mock_time + dt.timedelta(days=days_to_add)
        
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = mock_time
            
            interval_seconds = get_next_check_interval()
            interval_minutes = interval_seconds // 60
            
            status = "✅ PASS" if interval_minutes == expected_minutes else "❌ FAIL"
            
            print(f"{status} {description}")
            print(f"    Expected: {expected_minutes} min, Got: {interval_minutes} min")
            
            if interval_minutes != expected_minutes:
                print(f"    ERROR: Expected {expected_minutes} minutes but got {interval_minutes} minutes")
            
            print()

def demonstrate_daily_schedule():
    """Demonstrate the scheduling pattern for a typical weekday"""
    
    print("\nDaily Schedule Demonstration (Typical Weekday)")
    print("=" * 50)
    
    # Test every hour from midnight to 11 PM
    for hour in range(24):
        mock_time = dt.datetime(2024, 1, 1, hour, 0, 0, tzinfo=dt.timezone(dt.timedelta(hours=8)))
        # Make it a Monday (weekday=0)
        mock_time = mock_time + dt.timedelta(days=-mock_time.weekday())
        
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = mock_time
            
            interval_seconds = get_next_check_interval()
            interval_minutes = interval_seconds // 60
            
            time_str = f"{hour:02d}:00"
            
            if interval_minutes >= 60:
                interval_str = f"{interval_minutes//60}h"
            else:
                interval_str = f"{interval_minutes}m"
            
            # Add context about market status
            if 9.5 <= hour < 16:
                context = "📈 Market Hours"
            elif 8 <= hour < 9.5:
                context = "⏰ Pre-Market"
            elif 16 < hour <= 16.5:
                context = "📉 Post-Market"
            else:
                context = "🌙 Off-Hours"
            
            print(f"{time_str} HKT → Next check in {interval_str:>4} {context}")

if __name__ == "__main__":
    test_scheduling_logic()
    demonstrate_daily_schedule()
    
    print("\n" + "=" * 60)
    print("Summary of Enhanced Market Status Worker Benefits:")
    print("✅ Intelligent scheduling based on market hours")
    print("✅ Frequent checks (5 min) around market open/close")
    print("✅ Regular checks (30 min) during market hours")
    print("✅ Reduced checks (2-4 hours) during off-hours/weekends")
    print("✅ Significant reduction in unnecessary API calls")
    print("✅ More responsive to market status changes when needed")
