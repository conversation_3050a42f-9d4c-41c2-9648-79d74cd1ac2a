"""
Logging configuration for CBBC Chart application with Hong Kong timezone support.
Provides daily rotating log files with YYMMDD format and separate loggers for different components.
"""

import logging
import logging.handlers
import datetime as dt
import os
from typing import Optional


class HongKongTimezoneFormatter(logging.Formatter):
    """Custom formatter that uses Hong Kong timezone for log timestamps."""
    
    def __init__(self, fmt=None, datefmt=None):
        super().__init__(fmt, datefmt)
        self.hk_tz = dt.timezone(dt.timedelta(hours=8))
    
    def formatTime(self, record, datefmt=None):
        """Override formatTime to use Hong Kong timezone."""
        # Convert timestamp to Hong Kong time
        hk_time = dt.datetime.fromtimestamp(record.created, tz=self.hk_tz)
        
        if datefmt:
            return hk_time.strftime(datefmt)
        else:
            return hk_time.strftime('%Y-%m-%d %H:%M:%S HKT')


class DailyRotatingFileHandler(logging.handlers.TimedRotatingFileHandler):
    """Custom rotating file handler that creates daily log files with YYMMDD format."""
    
    def __init__(self, filename_prefix: str, log_dir: str = 'logs'):
        """
        Initialize the handler.
        
        Args:
            filename_prefix: Prefix for log files (e.g., 'market_status', 'auto_update')
            log_dir: Directory to store log files
        """
        self.log_dir = log_dir
        self.filename_prefix = filename_prefix
        self.hk_tz = dt.timezone(dt.timedelta(hours=8))
        
        # Create logs directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
        
        # Generate initial filename with current HK date
        current_filename = self._get_current_filename()
        
        # Initialize with midnight rotation in HK timezone
        super().__init__(
            filename=current_filename,
            when='midnight',
            interval=1,
            backupCount=30,  # Keep 30 days of logs
            encoding='utf-8',
            utc=False
        )
        
        # Override the rotation time calculation to use HK timezone
        self._setup_hk_rotation()
    
    def _get_current_filename(self) -> str:
        """Generate filename with YYMMDD format using Hong Kong timezone."""
        hk_now = dt.datetime.now(self.hk_tz)
        date_str = hk_now.strftime('%y%m%d')
        return os.path.join(self.log_dir, f'{date_str}_{self.filename_prefix}.log')
    
    def _setup_hk_rotation(self):
        """Setup rotation to occur at midnight Hong Kong time."""
        # Calculate next midnight in Hong Kong timezone
        hk_now = dt.datetime.now(self.hk_tz)
        next_midnight = (hk_now + dt.timedelta(days=1)).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        
        # Convert to UTC timestamp for the handler
        self.rolloverAt = next_midnight.timestamp()
    
    def doRollover(self):
        """Perform rollover with HK timezone-based filename."""
        if self.stream:
            self.stream.close()
            self.stream = None
        
        # Generate new filename for the new day
        new_filename = self._get_current_filename()
        self.baseFilename = new_filename
        
        # Setup next rollover time
        self._setup_hk_rotation()
        
        # Open new file
        if not self.delay:
            self.stream = self._open()


def setup_logging(log_level: str = 'INFO', log_dir: str = 'logs') -> None:
    """
    Setup comprehensive logging configuration for the CBBC Chart application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to store log files
    """
    
    # Create logs directory
    os.makedirs(log_dir, exist_ok=True)
    
    # Define log format
    log_format = '[%(asctime)s] %(levelname)-8s [%(name)s] %(message)s'
    
    # Create custom formatter with Hong Kong timezone
    formatter = HongKongTimezoneFormatter(log_format)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Create main application log handler
    main_handler = DailyRotatingFileHandler('main_app', log_dir)
    main_handler.setLevel(getattr(logging, log_level.upper()))
    main_handler.setFormatter(formatter)
    root_logger.addHandler(main_handler)


def get_logger(name: str, log_dir: str = 'logs') -> logging.Logger:
    """
    Get a logger with a separate daily rotating file handler.
    
    Args:
        name: Logger name (e.g., 'market_status_worker', 'auto_update_worker')
        log_dir: Directory to store log files
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Check if this logger already has a file handler to avoid duplicates
    has_file_handler = any(
        isinstance(handler, DailyRotatingFileHandler) 
        for handler in logger.handlers
    )
    
    if not has_file_handler:
        # Create dedicated file handler for this logger
        file_handler = DailyRotatingFileHandler(name, log_dir)
        file_handler.setLevel(logging.INFO)
        
        # Create formatter
        log_format = '[%(asctime)s] %(levelname)-8s [%(name)s] %(message)s'
        formatter = HongKongTimezoneFormatter(log_format)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        
        # Prevent propagation to avoid duplicate logs in main log
        logger.propagate = False
        
        # Also add console handler for immediate feedback
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger


def get_hk_time() -> dt.datetime:
    """
    Get current time in Hong Kong timezone.
    
    Returns:
        Current datetime in Hong Kong timezone
    """
    return dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))


def format_hk_time(dt_obj: Optional[dt.datetime] = None, format_str: str = '%Y-%m-%d %H:%M:%S HKT') -> str:
    """
    Format datetime object as Hong Kong time string.
    
    Args:
        dt_obj: Datetime object to format (uses current time if None)
        format_str: Format string for strftime
        
    Returns:
        Formatted time string in Hong Kong timezone
    """
    if dt_obj is None:
        dt_obj = get_hk_time()
    elif dt_obj.tzinfo is None:
        # Assume UTC if no timezone info
        dt_obj = dt_obj.replace(tzinfo=dt.timezone.utc)
    
    # Convert to Hong Kong timezone
    hk_time = dt_obj.astimezone(dt.timezone(dt.timedelta(hours=8)))
    return hk_time.strftime(format_str)


# Example usage and testing
if __name__ == '__main__':
    # Setup logging
    setup_logging('DEBUG')
    
    # Test main logger
    main_logger = logging.getLogger('main')
    main_logger.info('Main application started')
    
    # Test specialized loggers
    market_logger = get_logger('market_status_worker')
    market_logger.info('Market status worker initialized')
    
    auto_update_logger = get_logger('auto_update_worker')
    auto_update_logger.info('Auto update worker initialized')
    
    # Test Hong Kong time functions
    print(f"Current HK time: {format_hk_time()}")
    print(f"Log files will be created in 'logs' directory with YYMMDD format")
