# HSI CBBC Market Dashboard

## Project Overview
A Dash/Flask application for visualizing Hong Kong Stock Exchange (HKEX) Callable Bull/Bear Contracts (CBBC) data with real-time market status integration.

## File Structure
```
├── app.py                  # Main application with Dash UI and Flask server
├── saveCBBC_XLS.py         # Data processing and Excel generation module
├── get_xlsx.py            # Excel file reading and parsing utility
├── download_warrant_data.py # Warrant data download functionality
├── post_CBBC_XLS.py       # Excel data posting utility
├── requirements.txt       # Python dependencies
├── Procfile                # Deployment configuration
├── tests/                  # Test suite
│   ├── test.py
│   ├── test_market_status.py
│   └── ...                # Additional test files
├── download/              # Storage for generated images
├── src/main.py             # Core application logic (possibly for backend processing)
└── memory-bank/           # Data persistence and logging directory
```

## Key Features
- **Real-time Market Status**: Integrates with market-clock.com for HKEX status detection
- **Auto-update Functionality**: Background workers for scheduled data refresh
- **Multiple Chart Types**:
  - Overlay chart (default view)
  - Side-by-side comparison
  - Turnover-only view
  - OS value-only view
- **Dynamic Scaling**: Automatic and manual scale ratio adjustment for side-by-side charts
- **REST API Endpoints**: For external integration and status control

## Dependencies
- dash==2.17.0
- flask==3.0.0
- yfinance==0.2.30
- pandas==2.2.2
- numpy==2.0.2
- sqlalchemy==2.0.3
- python-dotenv==1.0.1
- requests==2.32.3
- beautifulsoup4==4.12.3
- plotly==5.24.1
- kaleido==0.2.1

## Usage
1. Install dependencies: `pip install -r requirements.txt`
2. Set environment variables:
   - `LOCAL_DATABASE_URL` for development
   - `HEROKU_DATABASE_URL` for production
   - `DATABASE_URL` for general use
3. Run application: `python app.py`
4. Access dashboard at http://localhost:8050

## Deployment
Application ready for Heroku deployment via Procfile:
```bash
web: gunicorn --bind :$PORT --workers 1 --timeout 120 app:server
```

## API Endpoints
- `GET /api/market-status` - Retrieve current market status
- `POST /api/toggle-auto-update` - Control auto-update functionality
- `POST /api/set-update-interval` - Configure update intervals
- `POST /api/manual-update` - Trigger manual data refresh

## Data Flow
1. `saveCBBC_XLS.py` fetches market data
2. Data stored in SQL database
3. Dash frontend queries database and generates visualizations
4. Chart images saved to `download/` directory
5. Market status worker intelligently schedules status checks

## Development Tools
- **Testing**: Comprehensive test suite in `/tests` directory
- **Data Storage**: JSON data files in root, Excel files in `/xls`
- **Logging**: Integrated with Python logging module
- **Threading**: Uses daemon threads for background tasks
