# %%
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
import pandas as pd

options = webdriver.ChromeOptions()
options.add_argument('--ignore-ssl-errors=yes')
options.add_argument('--ignore-certificate-errors')
options.add_argument('--disable-dev-shm-usage')

# platform=os.environ.get('platform')
# print(f"{platform=}")
# chrome_path=os.environ.get('heroku ps')
# print(f"{chrome_path=}")
# chrome_driver_path=os.environ.get('CHROMEDRIVER_PATH')
# print(f"{chrome_driver_path=}")
# db = os.environ.get('DATABASE_URL')
# print(f"{db=}")
# remote_db = create_engine(db)
if __name__ == '__main__':
    class infine_scroll(object):
        def __init__(self, last):
            self.last = last
        def __call__(self, driver):
            new = driver.execute_script('return document.body.scrollHeight')  
            if new > self.last:
                return new
            else:
                return False

    url = "http://www.aastocks.com/en/stocks/cbbc/search.aspx?t=1&s=&o=1&p=&symbol=110000&search=A|||||||||"
    # s=Service(chrome_driver_path)
    # driver = webdriver.Chrome(service=s)
    # Run HOST Chrome
    #driver = webdriver.Remote("http://127.0.0.1:4444/wd/hub", DesiredCapabilities.CHROME)
    # Run Docker Chrome
    # driver = webdriver.Remote("http://host.docker.internal:4444/wd/hub", DesiredCapabilities.CHROME)
    # Run Integrate Chrome
    try:
        driver = webdriver.Remote(
            command_executor='http://localhost:4444/wd/hub',
            options=options
        )
        # driver = webdriver.Chrome(chrome_driver_path)
        # driver.get("https://google.com")
        driver.get(url)
        last_height = driver.execute_script('return document.body.scrollHeight')
        print(f"{last_height=}")
        flag=1
        while flag==1:
            driver.execute_script('window.scrollTo(0,document.body.scrollHeight)')
            try:
                wait = WebDriverWait(driver, 1) # 10 sec optimal
                new_height = wait.until(infine_scroll( last_height))
                print(f"{new_height=}")
                last_height = new_height
            except:
                print(f"End of page reached at: {last_height}")
                flag = 0
        # Information is delayed for at least 15 minutes. Last Update: 2021/07/16 16:08
        # time_stamp = driver.find_elements_by_class_name('tabPanel_RemarksLastUpdate')[0].text[-16:].replace('/','').replace(' ','_').replace(':','')
        time_stamp = driver.find_elements(By.CLASS_NAME, 'tabPanel_RemarksLastUpdate')[0].text[-16:].replace('/','').replace(' ','_').replace(':','')
        # Extract Main Quote Tables
        tb = driver.find_elements(By.ID,'tblTS2')
        # tb = driver.find_elements_by_id('tblTS2')
        df  = pd.read_html(tb[0].get_attribute('outerHTML'))[0]
        print(f"{len(df)} Rows Retrived")
        print(df.tail())
    finally:
        if driver:
            driver.close()
            driver.quit()


