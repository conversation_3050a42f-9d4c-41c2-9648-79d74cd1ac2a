#%%
#!/usr/bin/env python3
"""
Script to download warrant/CBBC data from AAStocks and save as Excel file.
"""

import requests
import urllib.parse
from datetime import datetime
import os
import sys

# Import Hong Kong timezone utilities
try:
    from logging_config import get_hk_time, format_hk_time
    HK_TIMEZONE_AVAILABLE = True
except ImportError:
    # Fallback if logging_config is not available
    import datetime as dt
    HK_TIMEZONE_AVAILABLE = False

    def get_hk_time():
        return dt.datetime.now(dt.timezone(dt.timedelta(hours=8)))

def download_warrant_data():
    """Download warrant/CBBC data from AAStocks and save as Excel file."""
    
    # Decode the URL from the curl command
    base_url = "http://www.aastocks.com/en/resources/datafeed/getwarrantcbbcdata.ashx"
    
    # URL parameters (decoded from the curl command)
    params = {
        't': '2',
        'co': '|1|2|3|4|5|6|8|9|11|12|17|26|27|23|19|20|21|7|13|25|29|',
        's': '',
        'o': '',
        'f': '|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||0|0|0|0|0',
        'pi': '2',
        'exp': 'Y',
        'ps': '20'
    }
    
    # Headers from the curl command
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh-HK;q=0.6,zh;q=0.5',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Referer': 'http://www.aastocks.com/en/stocks/warrantcbbc/search.aspx?tab=2&symbol=110000',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
        'X-KL-kfa-Ajax-Request': 'Ajax_Request',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    # Cookies from the curl command (simplified - keeping essential ones)
    cookies = {
        'mLang': 'TC',
        '_ga': 'GA1.1.439800554.1729559890',
        'CookiePolicyCheck': '0'
    }
    
    try:
        print("Downloading warrant/CBBC data from AAStocks...")
        print(f"URL: {base_url}")
        print(f"Parameters: {params}")
        
        # Make the request
        response = requests.get(
            base_url,
            params=params,
            headers=headers,
            cookies=cookies,
            verify=False,  # Equivalent to --insecure in curl
            timeout=30
        )
        
        # Check if request was successful
        response.raise_for_status()
        
        print(f"Response status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        print(f"Content-Length: {len(response.content)} bytes")
        
        # Generate filename with timestamp using Hong Kong timezone
        if HK_TIMEZONE_AVAILABLE:
            timestamp = get_hk_time().strftime("%Y%m%d_%H%M%S")
        else:
            timestamp = get_hk_time().strftime("%Y%m%d_%H%M%S")
        filename = f"warrant_cbbc_data_{timestamp}.xlsx"
        
        # Check if the response contains Excel data
        content_type = response.headers.get('Content-Type', '').lower()
        
        if 'excel' in content_type or 'spreadsheet' in content_type:
            # Direct Excel file
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"Excel file saved as: {filename}")
            
        elif 'json' in content_type or response.text.strip().startswith('{'):
            # JSON response - might need to be converted to Excel
            print("Received JSON response. Content preview:")
            print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
            
            # Save as JSON first
            json_filename = f"warrant_cbbc_data_{timestamp}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"JSON data saved as: {json_filename}")
            
            # Try to convert to Excel if pandas is available
            try:
                import pandas as pd
                import json
                
                data = json.loads(response.text)
                
                # If data is a list of records, convert to DataFrame
                if isinstance(data, list) and len(data) > 0:
                    df = pd.DataFrame(data)
                elif isinstance(data, dict):
                    # If it's a dict, try to find the main data array
                    for key, value in data.items():
                        if isinstance(value, list) and len(value) > 0:
                            df = pd.DataFrame(value)
                            break
                    else:
                        # If no list found, convert the dict itself
                        df = pd.DataFrame([data])
                else:
                    print("Unable to convert JSON to DataFrame - unexpected structure")
                    return json_filename
                
                # Save as Excel
                df.to_excel(filename, index=False)
                print(f"Converted to Excel and saved as: {filename}")
                
            except ImportError:
                print("pandas not available - install with: pip install pandas openpyxl")
                print("JSON file saved instead.")
                return json_filename
            except Exception as e:
                print(f"Error converting to Excel: {e}")
                print("JSON file saved instead.")
                return json_filename
                
        else:
            # Unknown content type - save as binary and let user decide
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"Unknown content type. File saved as: {filename}")
            
            # Also save a preview as text
            try:
                preview_filename = f"warrant_cbbc_preview_{timestamp}.txt"
                with open(preview_filename, 'w', encoding='utf-8') as f:
                    f.write(f"Content-Type: {content_type}\n")
                    f.write(f"Content-Length: {len(response.content)}\n")
                    f.write("Content preview:\n")
                    f.write(response.text[:1000])
                print(f"Content preview saved as: {preview_filename}")
            except:
                pass
        
        return filename
        
    except requests.exceptions.RequestException as e:
        print(f"Error downloading data: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def main():
    """Main function."""
    print("AAStocks Warrant/CBBC Data Downloader")
    print("=" * 40)
    
    filename = download_warrant_data()
    
    if filename:
        print(f"\nDownload completed successfully!")
        print(f"File saved as: {filename}")
        print(f"File size: {os.path.getsize(filename)} bytes")
    else:
        print("\nDownload failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()

# %%
