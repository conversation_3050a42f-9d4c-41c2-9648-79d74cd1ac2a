version: '3.8'
services:
  devcontainer:
    # https://github.com/devcontainers/images/tree/main/src/python
    # image: mcr.microsoft.com/devcontainers/python:3.11-bookworm
    build: .
    volumes:
      - ..:/workspace:cached
      - C:\output\CBBC2\xls:/download
      - C:\output\CBBC2\output:/output
    network_mode: service:selenium
    command: sleep infinity

  selenium:
    # https://github.com/seleniumhq-community/docker-seleniarm
    image: seleniarm/standalone-chromium:latest
    volumes:
      - C:\output\CBBC2\xls:/home/<USER>/Downloads
    restart: unless-stopped
    ports:
      - "4444:4444"
      - "5900:5900"
      - "7900:7900"
