# %%
# import gunicorn
import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import os
from sqlalchemy import create_engine
from dash import dcc, html, Input, Output, dash_table
import plotly.graph_objects as go
import dash
# from dash_table import DataTable
from flask import send_from_directory
import plotly.io as pio
# from autoSaveCBBC import getCBBC
# from PIL import Image
from logging.config import dictConfig

app = dash.Dash(__name__, )
server = app.server
app.layout = html.Div([
    html.H4("CBBC Chart")])

@server.route('/hi')
def hello_world():
    return 'Hello, Docker!'

#  retrieve file from 'static/images' directory
@server.route('/img/<arg>')
def send_image(arg):
    print(f"image: {arg=}")
    filename= arg.split(':')[0]
    return send_from_directory("static/images", filename)

# @app.route('/update_cbbc')
# def update_cbbc():
#     print("update_cbbc")
#     try:
#         t="0000"
#         t = getCBBC()
#         r=f"getCBBC() completed at {t=}"
#     except:
#         r= "getCBBC() Failed"
#     print(r)
#     return r

# @app.route('/run_cbbc')
# def run_cbbc():
#     print("run autoSaveCBBC")
#     try:
#         t="0000"
#         t = runCBBC()
#         r=f"run() completed at {t=}"
#     except:
#         r= "run() Failed"
#     print(r)
#     return r

if __name__ == '__main__':
    app.run_server(debug=True)

# %%
# t = getCBBC()
# %%
