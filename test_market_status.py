#!/usr/bin/env python3
"""
Test script for market status checking functionality
"""

import requests
from bs4 import BeautifulSoup
import datetime as dt

def test_market_status_check():
    """
    Test the market status checking function
    """
    try:
        url = "https://www.market-clock.com/markets/hkex/equities/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        print(f"Testing market status check at {dt.datetime.now()}")
        print(f"Fetching: {url}")
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        print(f"Response status: {response.status_code}")
        print(f"Response length: {len(response.content)} bytes")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for the market status element
        status_element = soup.select_one('div.market-status-text, div.status-text, .market-status')
        
        if status_element:
            status_text = status_element.get_text().strip().lower()
            is_open = 'open' in status_text and 'closed' not in status_text
            print(f"Found status element: '{status_text}'")
            print(f"Market is: {'OPEN' if is_open else 'CLOSED'}")
        else:
            # Fallback: try to find any element containing status information
            all_text = soup.get_text().lower()
            is_open = 'market is open' in all_text or ('open' in all_text and 'closed' not in all_text)
            print("No specific status element found, using fallback method")
            print(f"Market is: {'OPEN' if is_open else 'CLOSED'}")
        
        # Print some of the page content for debugging
        print("\nFirst 500 characters of page content:")
        print(soup.get_text()[:500])
        
        return is_open
        
    except Exception as e:
        print(f"Error checking market status: {e}")
        return False

def test_api_endpoints():
    """
    Test the API endpoints (requires the app to be running)
    """
    base_url = "http://localhost:8050"
    
    endpoints = [
        "/api/market-status",
        "/api/toggle-auto-update",
        "/api/set-update-interval",
        "/api/manual-update"
    ]
    
    print("\nTesting API endpoints (requires app to be running):")
    
    for endpoint in endpoints:
        try:
            if endpoint == "/api/market-status":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                # For POST endpoints, send test data
                if endpoint == "/api/set-update-interval":
                    data = {"interval": 15}
                elif endpoint == "/api/toggle-auto-update":
                    data = {"enabled": True}
                else:
                    data = {}
                response = requests.post(f"{base_url}{endpoint}", json=data, timeout=5)
            
            print(f"{endpoint}: {response.status_code} - {response.json()}")
            
        except requests.exceptions.ConnectionError:
            print(f"{endpoint}: Connection refused (app not running)")
        except Exception as e:
            print(f"{endpoint}: Error - {e}")

if __name__ == "__main__":
    print("=== Market Status Monitoring Test ===")
    
    # Test market status checking
    test_market_status_check()
    
    # Test API endpoints
    test_api_endpoints()
    
    print("\n=== Test Complete ===")
