# %%
import time
import datetime as dt
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd

import sqlalchemy
from sqlalchemy import create_engine
from dotenv import load_dotenv
import os

download_path ='/download/'
def roundUpToMultiple(number, multiple):
    num = number + (multiple - 1)
    return num - (num % multiple)

def roundDnToMultiple(number, multiple):
    return number - (number % multiple)  

load_dotenv()
platform=os.environ.get('platform')
chrome_path=os.environ.get('heroku ps')
chrome_driver_path=os.environ.get('CHROMEDRIVER_PATH')
if platform == 'local':
    db=os.environ.get('LOCAL_DATABASE_URL')
elif platform == 'heroku':
    db = os.environ.get('HEROKU_DATABASE_URL')
else:
    db = os.environ.get('DATABASE_URL')

# Import logging configuration for Hong Kong timezone
from logging_config import get_hk_time, format_hk_time, get_logger

# Setup logger for this script
post_logger = get_logger('post_CBBC_worker')

post_logger.info(f"Platform: {platform}")
post_logger.debug(f"Chrome path: {chrome_path}")
post_logger.debug(f"Chrome driver path: {chrome_driver_path}")
post_logger.info(f"Database configured: {bool(db)}")

remote_db = create_engine(db, isolation_level="AUTOCOMMIT")


# %%
# Import Datafile
datafile = os.listdir(download_path)[0]
# time_stamp = "20240106_0000" #datafile[16:-5]
# Get time now in HKG timezone using standardized function
time_stamp = get_hk_time().strftime("%Y%m%d_%H%M")
post_logger.info(f"Processing timestamp: {time_stamp}")

df = pd.read_excel(download_path+datafile)
post_logger.info(f"READ {download_path+datafile}: {len(df)} rows")

# Filter df with underlying=='HSI'
df = df[df['Underlying'] == 'HSI'].copy()

# Cleasing
df.dropna(subset=['Last'], inplace=True)
if df['O/S'].dtypes =='object':
    df['OS_QTY2']=df['O/S'].str.replace('K','E+3').str.replace('M','E+6')
    df['OS_QTY2']=pd.to_numeric(df['OS_QTY2'], downcast='integer')
else:
    df['OS_QTY2']=df['O/S']
df['OS_VAL']=round(df['OS_QTY2']*df['Last'],0)
if df['Turn.'].dtypes =='object':
    df['TURNOVER']=df['Turn.'].str.replace('K','E+3').str.replace('M','E+6').str.replace('B','E+9')
    df['TURNOVER']=pd.to_numeric(df['TURNOVER'], downcast='integer')
else:
    df['TURNOVER']=df['Turn.']
# df['TURN_AMT']=round(df['TURNOVER']*df['Last#'], -3)
df['TURN_AMT']=round(df['TURNOVER'])

# Drop rows with 0 OS_VAL and TURN_AMT
df= df[(df['OS_VAL'] != 0) | (df['TURN_AMT'] != 0)]

df['CELL_RANGE']= df['Call Lv'].apply(lambda s: roundDnToMultiple(s, 100))

# SORT BY CELL_RANGE/OS_AMT
df_cum_bull = df[df['Bull/Bear']=='Bull'].groupby(['Call Lv']).agg({'OS_VAL':'sum', 'TURN_AMT':'sum'})
df_cum_bull = df_cum_bull.sort_values(by='Call Lv', ascending=False).reset_index()
df_cum_bull['CUM_OS_VAL']=df_cum_bull['OS_VAL'].cumsum()
df_cum_bull['Bull/Bear']='Bull'

df_cum_bear = df[df['Bull/Bear']=='Bear'].groupby(['Call Lv']).agg({'OS_VAL':'sum', 'TURN_AMT':'sum'}).reset_index()
# df_cum_bear = df_cum_bear.sort_values(by='Call Lv', ascending=False)
df_cum_bear['CUM_OS_VAL']=df_cum_bear['OS_VAL'].cumsum()
df_cum_bear['Bull/Bear']='Bear'

df_cum = pd.concat([df_cum_bull, df_cum_bear])
df_cum['txn_date']= time_stamp
# Save to database
df_cum.rename(columns = {'Bull/Bear':'BULLBEAR', 'Call Lv':'CallLv'}, inplace = True) 
tname='hsi_cbbc_cum'
df_cum.to_sql(name= tname, con=remote_db, if_exists = 'replace', index=False,
    dtype={'BULLBEAR': sqlalchemy.types.VARCHAR(length=20)}
    )    

# Create Summary df
# df_near = df[(df['CELL_RANGE']>= 18000) & (df['CELL_RANGE']<= 26000) ]
# df_near = df[(df['CELL_RANGE']>= 18000) & (df['CELL_RANGE']<= 22000) & ((df['OS_VAL']> 0 + df['TURNOVER'])>0)]
df_sum = df.groupby(['CELL_RANGE','Bull/Bear']).agg({'OS_VAL':'sum', 'TURN_AMT':'sum'}).reset_index()
# df_sum = df_sum[ (df_sum['OS_VAL']>0) + (df_sum['TURN_AMT']>0)]
df_sum['txn_date']= time_stamp

# Save to database
df_sum.rename(columns = {'Bull/Bear':'BULLBEAR'}, inplace = True) 
tname='hsi_cbbc_sum'
df_sum.to_sql(name= tname, con=remote_db, if_exists = 'replace', index=False,
    dtype={'BULLBEAR': sqlalchemy.types.VARCHAR(length=20)}
    )    

# Archive to History table
# sql_delete=f"DELETE FROM public.hsi_cbbc_sum_hist where trade_date = '{time_stamp[0:8]}';"
# sql_insert=f"INSERT INTO public.hsi_cbbc_sum_hist SELECT '{time_stamp[0:8]}',* FROM public.hsi_cbbc_sum;"
# with remote_db.connect() as con:
#     rs = con.execute(sql_delete)
#     rs = con.execute(sql_insert)
#%%
if platform == 'local':
    out_path = os.environ.get('out_path')
    post_logger.debug(f"Output path: {out_path}")
    pathname=str(f'{out_path}{time_stamp[0:8]}/')
    if not os.path.exists(pathname):
        os.mkdir(pathname)
        post_logger.info(f"Directory Created: {pathname}")
    df.to_excel(pathname + f'cbbc_{time_stamp}_raw.xlsx', encoding='UTF8', index = False)
    df_sum.to_excel(pathname + f'cbbc_{time_stamp}_sum.xlsx', encoding='UTF8', index = False)
    post_logger.info(f"Excel files saved to {pathname}")
    df_cum.sort_values(by='CallLv', ascending=True).to_excel(pathname + f'cbbc_{time_stamp}_cum.xlsx', encoding='UTF8', index = False)
# return time_stamp


# %%
