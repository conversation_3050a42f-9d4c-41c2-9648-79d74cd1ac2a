# Market Status Monitoring and Auto-Update Features

## Overview
This document describes the new market status monitoring and automated CBBC data update features added to the CBBC Chart application.

## Features Implemented

### 1. Market Status Monitoring
- **Automatic Market Status Detection**: Checks Hong Kong Stock Exchange status every hour
- **Source**: https://www.market-clock.com/markets/hkex/equities/
- **Fallback Logic**: Time-based detection using HK market hours (9:30 AM - 4:00 PM HKT, Monday-Friday)
- **Global Variable**: `market_open_indicator` (Boolean)

### 2. Automated CBBC Data Updates
- **Background Task**: Runs `saveCBBC_XLS()` at configurable intervals when market is open
- **Configurable Intervals**: 5, 15, 30, or 60 minutes
- **Market-Aware**: Only runs when `market_open_indicator` is True
- **Global Variables**: 
  - `auto_update_enabled` (Boolean)
  - `update_interval` (Integer, minutes)

### 3. User Interface Controls
- **Market Status Toggle**: Radio button to manually override market status
- **Auto-Update Toggle**: Radio button to enable/disable automatic updates
- **Update Interval Dropdown**: Select update frequency (5, 15, 30, 60 minutes)
- **Real-time Updates**: UI controls update global state immediately

### 4. API Endpoints

#### GET /api/market-status
Returns current market status and auto-update settings.

**Response:**
```json
{
  "market_open": true,
  "auto_update_enabled": false,
  "update_interval": 15,
  "last_checked": "2025-06-30T01:56:40.821086"
}
```

#### POST /api/toggle-auto-update
Toggle or set auto-update functionality.

**Request Body (optional):**
```json
{
  "enabled": true
}
```

**Response:**
```json
{
  "auto_update_enabled": true,
  "message": "Auto-update enabled"
}
```

#### POST /api/set-update-interval
Set the auto-update interval.

**Request Body:**
```json
{
  "interval": 15
}
```

**Response:**
```json
{
  "update_interval": 15,
  "message": "Update interval set to 15 minutes"
}
```

#### POST /api/manual-update
Manually trigger CBBC data update.

**Response:**
```json
{
  "success": true,
  "message": "Manual update completed successfully",
  "start_time": "2025-06-30T01:56:40.000000",
  "end_time": "2025-06-30T01:57:15.000000",
  "duration_seconds": 35.0
}
```

## Technical Implementation

### Background Tasks
- **Threading**: Uses Python's `threading` module for background tasks
- **Daemon Threads**: Background tasks run as daemon threads
- **Thread Safety**: Uses `threading.Lock()` for thread-safe global variable updates
- **Auto-Start**: Background tasks start automatically when the app launches

### Market Status Detection
1. **Primary Method**: Web scraping from market-clock.com
2. **Fallback Method**: Time-based logic using Hong Kong timezone
3. **Error Handling**: Graceful fallback on network errors
4. **Hourly Updates**: Market status checked every hour automatically

### Global State Management
- **Thread-Safe Updates**: All global variable updates use thread locks
- **Real-time Sync**: UI controls immediately update global state
- **Persistent State**: Settings persist during app session

## Usage Instructions

### For End Users
1. **View Market Status**: Check the "Market Status" radio buttons on the home page
2. **Enable Auto-Updates**: Toggle the "Auto-Update" radio button to "Enabled"
3. **Set Update Frequency**: Select desired interval from the dropdown (5-60 minutes)
4. **Manual Override**: Use the Market Status radio button to manually set market status

### For Developers
1. **API Integration**: Use the REST API endpoints for programmatic control
2. **Monitoring**: Check console logs for background task status and errors
3. **Testing**: Use `test_market_status.py` to validate functionality

## Configuration

### Environment Variables
No additional environment variables required. The system uses existing database and platform configurations.

### Default Settings
- Market Status: Automatically detected
- Auto-Update: Disabled by default
- Update Interval: 15 minutes by default

## Error Handling
- **Network Errors**: Graceful fallback to time-based market detection
- **API Errors**: Proper HTTP status codes and error messages
- **Background Task Errors**: Automatic retry with exponential backoff
- **Thread Safety**: Prevents race conditions with proper locking

## Testing
Run the test script to validate functionality:
```bash
python test_market_status.py
```

## Future Enhancements
- **Database Persistence**: Store settings in database for persistence across restarts
- **Email Notifications**: Send alerts when auto-updates fail
- **Advanced Scheduling**: Support for custom time-based schedules
- **Market Holiday Detection**: Integration with Hong Kong market holiday calendar
