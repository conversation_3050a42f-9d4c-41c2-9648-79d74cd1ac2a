#!/usr/bin/env python3
"""
Test script to verify the callback error fix
"""

import sys
import datetime as dt
from unittest.mock import patch, MagicMock

def test_pandas_import_fix():
    """Test that pandas import conflicts are resolved"""
    
    print("Testing Pandas Import Fix")
    print("=" * 30)
    
    try:
        # Import the app module
        sys.path.append('.')
        from app import update_tbl, auto_update_worker
        import pandas as pd
        
        print("✅ Successfully imported app functions")
        print("✅ No UnboundLocalError for pandas")
        print(f"✅ Pandas version: {pd.__version__}")
        
        # Test that pandas is accessible in the global scope
        test_df = pd.DataFrame({'test': [1, 2, 3]})
        print(f"✅ Pandas DataFrame creation works: {len(test_df)} rows")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_status_tracking_variables():
    """Test that status tracking variables are properly initialized"""
    
    print("\nTesting Status Tracking Variables")
    print("=" * 35)
    
    try:
        from app import (
            last_update_time, next_update_time, last_update_rows, 
            last_update_success, last_market_check_time, next_market_check_time
        )
        
        print("✅ All status tracking variables imported successfully")
        print(f"   last_update_time: {last_update_time}")
        print(f"   next_update_time: {next_update_time}")
        print(f"   last_update_rows: {last_update_rows}")
        print(f"   last_update_success: {last_update_success}")
        print(f"   last_market_check_time: {last_market_check_time}")
        print(f"   next_market_check_time: {next_market_check_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_callback_function_structure():
    """Test that callback functions have correct structure"""
    
    print("\nTesting Callback Function Structure")
    print("=" * 37)
    
    try:
        from app import update_status_info, update_tbl
        import inspect
        
        # Check update_status_info function signature
        sig = inspect.signature(update_status_info)
        params = list(sig.parameters.keys())
        print(f"✅ update_status_info parameters: {params}")
        
        # Check update_tbl function signature
        sig = inspect.signature(update_tbl)
        params = list(sig.parameters.keys())
        print(f"✅ update_tbl parameters: {params}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def simulate_callback_execution():
    """Simulate callback execution to test for errors"""
    
    print("\nSimulating Callback Execution")
    print("=" * 30)
    
    try:
        from app import update_status_info
        
        # Mock the global variables
        with patch('app.last_update_time', dt.datetime.now()):
            with patch('app.next_update_time', dt.datetime.now() + dt.timedelta(minutes=15)):
                with patch('app.last_update_rows', 1250):
                    with patch('app.last_update_success', True):
                        with patch('app.market_open_indicator', True):
                            with patch('app.auto_update_enabled', True):
                                
                                # Call the status update function
                                result = update_status_info(1)
                                
                                print("✅ update_status_info executed successfully")
                                print(f"✅ Returned {len(result)} status elements")
                                
                                # Check that result contains expected elements
                                if isinstance(result, list) and len(result) > 0:
                                    print("✅ Status information properly formatted")
                                else:
                                    print("⚠️  Unexpected result format")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in callback simulation: {e}")
        return False

def main():
    """Run all tests"""
    
    print("CBBC Chart Callback Error Fix Verification")
    print("=" * 45)
    
    tests = [
        test_pandas_import_fix,
        test_status_tracking_variables,
        test_callback_function_structure,
        simulate_callback_execution
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 45)
    print("Test Results Summary:")
    print(f"✅ Passed: {sum(results)}/{len(results)} tests")
    
    if all(results):
        print("🎉 All tests passed! Callback error should be fixed.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    print("\nFix Summary:")
    print("• Removed local 'import pandas as pd' statements")
    print("• Using global pandas import instead")
    print("• No more UnboundLocalError for 'pd' variable")
    print("• Status tracking variables properly initialized")
    print("• Callback functions should work correctly now")

if __name__ == "__main__":
    main()
